#!/usr/bin/env python3
"""
验证 httpx 改进功能

简单验证 macOS M1 Chrome User-Agent 设置和 4xx 状态码错误处理功能。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_user_agent():
    """测试 User-Agent 设置"""
    print("🔍 测试 1: User-Agent 设置")
    print("=" * 50)
    
    try:
        from src.backend.agents.web_content_processor import WebScraper
        
        scraper = WebScraper()
        user_agent = scraper.client.headers.get("User-Agent")
        
        print(f"配置的 User-Agent: {user_agent}")
        
        # 验证关键标识符
        expected_identifiers = ["Macintosh", "Intel Mac OS X", "Chrome", "Safari", "AppleWebKit"]
        all_present = True
        
        for identifier in expected_identifiers:
            if identifier in user_agent:
                print(f"✅ 包含 {identifier}")
            else:
                print(f"❌ 缺少 {identifier}")
                all_present = False
        
        if all_present:
            print("✅ User-Agent 测试通过")
        else:
            print("❌ User-Agent 测试失败")
        
        print()
        return all_present
        
    except Exception as e:
        print(f"❌ User-Agent 测试异常: {e}")
        print()
        return False


def test_config_user_agent():
    """测试配置文件中的 User-Agent 设置"""
    print("⚙️ 测试 2: 配置文件 User-Agent 设置")
    print("=" * 50)
    
    try:
        from src.backend.config import NetworkConfig
        
        config = NetworkConfig()
        user_agent = config.user_agent
        
        print(f"配置的默认 User-Agent: {user_agent}")
        
        # 验证关键标识符
        expected_identifiers = ["Macintosh", "Intel Mac OS X", "Chrome", "Safari", "AppleWebKit"]
        all_present = True
        
        for identifier in expected_identifiers:
            if identifier in user_agent:
                print(f"✅ 包含 {identifier}")
            else:
                print(f"❌ 缺少 {identifier}")
                all_present = False
        
        if all_present:
            print("✅ 配置 User-Agent 测试通过")
        else:
            print("❌ 配置 User-Agent 测试失败")
        
        print()
        return all_present
        
    except Exception as e:
        print(f"❌ 配置 User-Agent 测试异常: {e}")
        print()
        return False


def test_4xx_error_handling():
    """测试 4xx 错误处理逻辑"""
    print("🚫 测试 3: 4xx 错误处理逻辑")
    print("=" * 50)
    
    try:
        from src.backend.agents.web_content_processor import WebScraper
        from src.backend.config import NetworkRetryConfig
        import httpx
        from unittest.mock import MagicMock
        
        # 创建 WebScraper 实例
        retry_config = NetworkRetryConfig(max_retries=3)
        scraper = WebScraper(retry_config=retry_config)
        
        # 测试 _should_retry 方法对 4xx 错误的处理
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_exception = httpx.HTTPStatusError("404 Not Found", request=MagicMock(), response=mock_response)
        
        should_retry = scraper._should_retry(mock_exception, 0)
        
        if not should_retry:
            print("✅ 4xx 错误不会被重试")
        else:
            print("❌ 4xx 错误仍然会被重试")
        
        # 测试不同的 4xx 状态码
        test_codes = [400, 401, 403, 404, 429, 499]
        all_correct = True
        
        for code in test_codes:
            mock_response.status_code = code
            mock_exception = httpx.HTTPStatusError(f"{code} Error", request=MagicMock(), response=mock_response)
            should_retry = scraper._should_retry(mock_exception, 0)
            
            if not should_retry:
                print(f"✅ {code} 错误不会被重试")
            else:
                print(f"❌ {code} 错误仍然会被重试")
                all_correct = False
        
        # 测试 5xx 错误仍然会被重试（如果配置允许）
        retry_config_with_5xx = NetworkRetryConfig(
            max_retries=3,
            retry_on_http_error=True,
            retry_http_status_codes=[500, 502, 503]
        )
        scraper_with_5xx = WebScraper(retry_config=retry_config_with_5xx)
        
        mock_response.status_code = 500
        mock_exception = httpx.HTTPStatusError("500 Internal Server Error", request=MagicMock(), response=mock_response)
        should_retry_5xx = scraper_with_5xx._should_retry(mock_exception, 0)
        
        if should_retry_5xx:
            print("✅ 5xx 错误仍然会被重试（当配置允许时）")
        else:
            print("❌ 5xx 错误不会被重试")
            all_correct = False
        
        if all_correct and not should_retry:
            print("✅ 4xx 错误处理测试通过")
        else:
            print("❌ 4xx 错误处理测试失败")
        
        print()
        return all_correct and not should_retry
        
    except Exception as e:
        print(f"❌ 4xx 错误处理测试异常: {e}")
        import traceback
        traceback.print_exc()
        print()
        return False


def main():
    """主测试函数"""
    print("🚀 httpx 改进功能验证")
    print("=" * 60)
    print()
    
    # 运行所有测试
    test_results = []
    
    test_results.append(test_user_agent())
    test_results.append(test_config_user_agent())
    test_results.append(test_4xx_error_handling())
    
    # 总结结果
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！")
        print()
        print("✅ 主要改进已成功实现:")
        print("1. User-Agent 已更新为 macOS M1 Chrome 浏览器")
        print("2. 4xx 状态码被正确处理，不会重试")
        print("3. 配置文件中的默认 User-Agent 已更新")
        return True
    else:
        print("❌ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    # 设置代理环境变量（如果需要）
    os.environ.setdefault('HTTP_PROXY', 'http://127.0.0.1:8118/')
    os.environ.setdefault('HTTPS_PROXY', 'http://127.0.0.1:8118/')
    
    # 运行验证
    success = main()
    sys.exit(0 if success else 1)
