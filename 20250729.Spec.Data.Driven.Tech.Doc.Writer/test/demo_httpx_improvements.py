"""
演示 httpx 改进功能

展示 macOS M1 Chrome User-Agent 设置和 4xx 状态码错误处理功能。
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.backend.agents.web_content_processor import WebScraper
from src.backend.config import NetworkRetryConfig
import structlog

# 配置日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


async def demo_user_agent():
    """演示 User-Agent 设置"""
    print("🔍 演示 1: User-Agent 设置")
    print("=" * 50)
    
    scraper = WebScraper()
    user_agent = scraper.client.headers.get("User-Agent")
    
    print(f"配置的 User-Agent: {user_agent}")
    print()
    
    # 验证关键标识符
    identifiers = ["Macintosh", "Intel Mac OS X", "Chrome", "Safari", "AppleWebKit"]
    for identifier in identifiers:
        if identifier in user_agent:
            print(f"✅ 包含 {identifier}")
        else:
            print(f"❌ 缺少 {identifier}")
    
    print()


async def demo_4xx_error_handling():
    """演示 4xx 错误处理"""
    print("🚫 演示 2: 4xx 错误处理")
    print("=" * 50)
    
    scraper = WebScraper()
    
    # 测试一些常见的 4xx 错误 URL
    test_urls = [
        "https://httpbin.org/status/404",  # 404 Not Found
        "https://httpbin.org/status/403",  # 403 Forbidden
        "https://httpbin.org/status/401",  # 401 Unauthorized
    ]
    
    for url in test_urls:
        print(f"测试 URL: {url}")
        try:
            result = await scraper.fetch_url(url)
            
            print(f"  成功: {result['success']}")
            print(f"  状态码: {result.get('status_code', 'N/A')}")
            print(f"  错误类型: {result.get('error_type', 'N/A')}")
            print(f"  错误信息: {result.get('error', 'N/A')}")
            print(f"  尝试次数: {result.get('attempts', 'N/A')}")
            print()
            
        except Exception as e:
            print(f"  异常: {e}")
            print()


async def demo_successful_request():
    """演示成功请求"""
    print("✅ 演示 3: 成功请求")
    print("=" * 50)
    
    scraper = WebScraper()
    
    # 测试成功的请求
    test_url = "https://httpbin.org/get"
    
    print(f"测试 URL: {test_url}")
    try:
        result = await scraper.fetch_url(test_url)
        
        print(f"  成功: {result['success']}")
        print(f"  状态码: {result.get('status_code', 'N/A')}")
        print(f"  内容大小: {result.get('content_size', 'N/A')} 字节")
        print(f"  获取时间: {result.get('fetch_time', 'N/A')} 秒")
        print(f"  尝试次数: {result.get('attempts', 'N/A')}")
        
        # 检查返回的内容中是否包含我们的 User-Agent
        content = result.get('content', '')
        if 'Macintosh' in content and 'Chrome' in content:
            print("  ✅ 服务器接收到了正确的 User-Agent")
        else:
            print("  ⚠️  无法确认服务器是否接收到正确的 User-Agent")
        
        print()
        
    except Exception as e:
        print(f"  异常: {e}")
        print()


async def demo_retry_behavior():
    """演示重试行为"""
    print("🔄 演示 4: 重试行为对比")
    print("=" * 50)
    
    # 配置重试策略
    retry_config = NetworkRetryConfig(
        max_retries=2,
        retry_on_http_error=True,
        retry_http_status_codes=[500, 502, 503]
    )
    scraper = WebScraper(retry_config=retry_config)
    
    # 测试 4xx（不应重试）
    print("测试 4xx 错误（不应重试）:")
    try:
        result = await scraper.fetch_url("https://httpbin.org/status/404")
        print(f"  尝试次数: {result.get('attempts', 'N/A')} (应该是 1)")
    except Exception as e:
        print(f"  异常: {e}")
    
    print()
    
    # 测试 5xx（应该重试）
    print("测试 5xx 错误（应该重试）:")
    try:
        result = await scraper.fetch_url("https://httpbin.org/status/500")
        print(f"  尝试次数: {result.get('attempts', 'N/A')} (应该是 3)")
    except Exception as e:
        print(f"  异常: {e}")
    
    print()


async def main():
    """主演示函数"""
    print("🚀 httpx 改进功能演示")
    print("=" * 60)
    print()
    
    try:
        await demo_user_agent()
        await demo_4xx_error_handling()
        await demo_successful_request()
        await demo_retry_behavior()
        
        print("🎉 演示完成！")
        print()
        print("主要改进:")
        print("1. ✅ User-Agent 已更新为 macOS M1 Chrome 浏览器")
        print("2. ✅ 4xx 状态码被标记为 URL 访问失败")
        print("3. ✅ 4xx 错误记录告警日志且不重试")
        print("4. ✅ 5xx 错误仍然按配置重试")
        
    except Exception as e:
        logger.error("演示过程中发生错误", error=str(e))
        print(f"❌ 演示失败: {e}")


if __name__ == "__main__":
    # 设置代理环境变量（如果需要）
    os.environ.setdefault('HTTP_PROXY', 'http://127.0.0.1:8118/')
    os.environ.setdefault('HTTPS_PROXY', 'http://127.0.0.1:8118/')
    
    # 运行演示
    asyncio.run(main())
